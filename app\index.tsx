import { useEffect, useState } from "react";
import { useRouter } from "expo-router";
import { RootState, useAppSelector } from "@/store/store";
import { LoadingOverlay, LocationPermissionModal } from "@/components";

export default function IndexScreen() {
  const router = useRouter();
  const token = useAppSelector((state: RootState) => state.auth.token);
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [hasNavigated, setHasNavigated] = useState(false);

  useEffect(() => {
    if (hasNavigated) return;

    if (!token) {
      router.replace("/(auth)/intro");
    } else {
      router.replace("/(protected)/(tabs)/home");
    }

    setHasNavigated(true);
  }, [token]);

  const handleLocationPermissionComplete = async () => {
    setShowLocationModal(false);
  };

  return (
    <>
      <LoadingOverlay isLoading={true} size="large" />
      <LocationPermissionModal
        isVisible={showLocationModal}
        onComplete={handleLocationPermissionComplete}
      />
    </>
  );
}
