import { Image,  View } from "react-native";
import { useFocusEffect, useRouter } from "expo-router";
import {
  Container,
  LinkText,
  LinkButton,
  TopContainer,
  BottomContentContainer,
  Question,
  RoleButton,
  RoleButtonText,
  ModalButtonContainer,
} from "@/styles/Intro.styles";
import { useTranslation } from "react-i18next";
import { useAppDispatch } from "@/store/store";
import { resetUser, setUser } from "@/store/slices/authSlice";
import { UserType } from "@/types/api";
import { useCallback, useState } from "react";
export default function IntroScreen() {
  const router = useRouter();
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const [selectedRole, setSelectedRole] = useState<number | null>(null);
  useFocusEffect(
    useCallback(() => {
      dispatch(resetUser());
      setSelectedRole(null);
    }, [dispatch])
  );
  const handleRoleSelect = (role_id: number) => {
    setSelectedRole(role_id);
    dispatch(setUser({ role_id: role_id }));
    router.push("/(auth)/register");
  };
  return (
    <Container contentContainerStyle={{ flex: 1 }}>
      <TopContainer>
        <Image
          source={require("../../assets/icon.png")}
          style={{ height: "80%", width: "100%" }}
          resizeMode="contain"
        />
      </TopContainer>
      <BottomContentContainer>
        <View style={{ flex: 1, width: "100%" }}>
          <Question>{t("intro.choose_role")}</Question>
          <ModalButtonContainer>
            <RoleButton
              isSelected={selectedRole === UserType.CUSTOMER}
              onPress={() => handleRoleSelect(UserType.CUSTOMER)}
            >
              <RoleButtonText isSelected={selectedRole === UserType.CUSTOMER}>
                {t("intro.customer_button")}
              </RoleButtonText>
            </RoleButton>
            <RoleButton
              isSelected={selectedRole === UserType.VENDOR}
              onPress={() => handleRoleSelect(UserType.VENDOR)}
            >
              <RoleButtonText isSelected={selectedRole === UserType.VENDOR}>
                {t("intro.vendor_button")}
              </RoleButtonText>
            </RoleButton>
          </ModalButtonContainer>
        </View>
        <LinkButton onPress={() => router.push("/(auth)/login")}>
          <LinkText>{t("intro.have_account")}</LinkText>
        </LinkButton>
      </BottomContentContainer>
    </Container>
  );
}